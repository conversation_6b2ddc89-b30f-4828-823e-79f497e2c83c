{"schema": {"version": {"fullVersion": "1.5.11", "major": "1", "minor": "5", "patch": "11"}, "meta": {"flags": ["experimentalData"]}, "collections": [{"label": "Pages", "name": "page", "path": "content/pages", "ui": {}, "templates": [{"name": "home", "label": "Home", "fields": [{"name": "blocks", "label": "Blocks", "type": "object", "list": true, "templates": [{"label": "Hero Section", "name": "hero", "fields": [{"type": "image", "label": "Hero Image", "name": "image", "namespace": ["page", "home", "blocks", "hero", "image"]}, {"type": "string", "label": "Heading", "name": "heading", "namespace": ["page", "home", "blocks", "hero", "heading"]}], "namespace": ["page", "home", "blocks", "hero"]}, {"label": "Company Values", "name": "values", "fields": [{"type": "image", "label": "Background Image", "name": "backgroundImage", "namespace": ["page", "home", "blocks", "values", "backgroundImage"]}, {"type": "string", "label": "Section Title", "name": "title", "namespace": ["page", "home", "blocks", "values", "title"]}, {"type": "object", "label": "Quality Value", "name": "quality1", "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["page", "home", "blocks", "values", "quality1", "title"]}, {"type": "rich-text", "label": "Description", "name": "description", "namespace": ["page", "home", "blocks", "values", "quality1", "description"]}], "namespace": ["page", "home", "blocks", "values", "quality1"]}, {"type": "object", "label": "On-Time Value", "name": "quality2", "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["page", "home", "blocks", "values", "quality2", "title"]}, {"type": "rich-text", "label": "Description", "name": "description", "namespace": ["page", "home", "blocks", "values", "quality2", "description"]}], "namespace": ["page", "home", "blocks", "values", "quality2"]}, {"type": "object", "label": "Collaboration Value", "name": "quality3", "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["page", "home", "blocks", "values", "quality3", "title"]}, {"type": "rich-text", "label": "Description", "name": "description", "namespace": ["page", "home", "blocks", "values", "quality3", "description"]}], "namespace": ["page", "home", "blocks", "values", "quality3"]}], "namespace": ["page", "home", "blocks", "values"]}, {"label": "Location", "name": "location", "fields": [{"type": "string", "label": "Location", "name": "location", "ui": {"component": "textarea"}, "namespace": ["page", "home", "blocks", "location", "location"]}], "namespace": ["page", "home", "blocks", "location"]}, {"label": "Machine Carousel", "name": "machineCarousel", "fields": [{"type": "string", "label": "Section Title", "name": "sectionTitle", "namespace": ["page", "home", "blocks", "machineCarousel", "sectionTitle"]}, {"type": "string", "label": "Button Label", "name": "buttonLabel", "namespace": ["page", "home", "blocks", "machineCarousel", "buttonLabel"]}, {"type": "reference", "collections": ["machines"], "name": "machineCards", "label": "Machine Cards", "namespace": ["page", "home", "blocks", "machineCarousel", "machineCards"]}], "namespace": ["page", "home", "blocks", "machineCarousel"]}, {"label": "Quote Section", "name": "quoteSection", "fields": [{"type": "object", "label": "Request Quote Form", "name": "requestQuoteForm", "fields": [{"type": "string", "label": "Form Title", "name": "formTitle", "namespace": ["page", "home", "blocks", "quoteSection", "requestQuoteForm", "formTitle"]}, {"type": "string", "label": "Form Field 1 Placeholder", "name": "field1Placeholder", "namespace": ["page", "home", "blocks", "quoteSection", "requestQuoteForm", "field1Placeholder"]}, {"type": "string", "label": "Form Field 2 Placeholder", "name": "field2Placeholder", "namespace": ["page", "home", "blocks", "quoteSection", "requestQuoteForm", "field2Placeholder"]}, {"type": "string", "label": "Form Field 3 Placeholder", "name": "field3Placeholder", "namespace": ["page", "home", "blocks", "quoteSection", "requestQuoteForm", "field3Placeholder"]}, {"type": "string", "label": "Submit <PERSON>ton Text", "name": "submitButtonText", "namespace": ["page", "home", "blocks", "quoteSection", "requestQuoteForm", "submitButtonText"]}], "namespace": ["page", "home", "blocks", "quoteSection", "requestQuoteForm"]}, {"type": "string", "label": "Request Copy Title", "name": "requestCopyTitle", "namespace": ["page", "home", "blocks", "quoteSection", "requestCopyTitle"]}, {"type": "rich-text", "label": "Fill Form Copy", "name": "fillFormCopy", "namespace": ["page", "home", "blocks", "quoteSection", "fillFormCopy"]}, {"type": "rich-text", "label": "<PERSON><PERSON>", "name": "emailCopy", "namespace": ["page", "home", "blocks", "quoteSection", "emailCopy"]}, {"type": "rich-text", "label": "Response Copy", "name": "responseCopy", "namespace": ["page", "home", "blocks", "quoteSection", "responseCopy"]}], "namespace": ["page", "home", "blocks", "quoteSection"]}, {"label": "Career Section", "name": "careerSection", "fields": [{"type": "string", "label": "Heading", "name": "heading", "namespace": ["page", "home", "blocks", "careerSection", "heading"]}, {"type": "rich-text", "label": "Career Section Description", "name": "careerDescription", "namespace": ["page", "home", "blocks", "careerSection", "careerDescription"]}, {"type": "string", "label": "Button Label", "name": "buttonLabel", "namespace": ["page", "home", "blocks", "careerSection", "buttonLabel"]}, {"type": "object", "label": "Training Card", "name": "card1", "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["page", "home", "blocks", "careerSection", "card1", "title"]}, {"type": "string", "label": "Description", "name": "card1Description", "namespace": ["page", "home", "blocks", "careerSection", "card1", "card1Description"]}], "namespace": ["page", "home", "blocks", "careerSection", "card1"]}, {"type": "object", "label": "Benefits Card", "name": "card2", "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["page", "home", "blocks", "careerSection", "card2", "title"]}, {"type": "string", "label": "Description", "name": "card2Description", "namespace": ["page", "home", "blocks", "careerSection", "card2", "card2Description"]}], "namespace": ["page", "home", "blocks", "careerSection", "card2"]}, {"type": "object", "label": "Growth Card", "name": "card3", "fields": [{"type": "string", "label": "Title", "name": "title", "namespace": ["page", "home", "blocks", "careerSection", "card3", "title"]}, {"type": "string", "label": "Description", "name": "card3Description", "namespace": ["page", "home", "blocks", "careerSection", "card3", "card3Description"]}], "namespace": ["page", "home", "blocks", "careerSection", "card3"]}], "namespace": ["page", "home", "blocks", "careerSection"]}], "namespace": ["page", "home", "blocks"], "searchable": true, "uid": false}], "namespace": ["page", "home"]}, {"name": "viewMachines", "label": "View Machines", "fields": [{"name": "blocks", "label": "Blocks", "type": "object", "list": true, "templates": [{"label": "Machines", "name": "machines", "fields": [{"name": "heading", "label": "Heading", "type": "string", "namespace": ["page", "viewMachines", "blocks", "machines", "heading"]}, {"name": "description", "label": "Description", "type": "string", "ui": {"component": "textarea"}, "namespace": ["page", "viewMachines", "blocks", "machines", "description"]}, {"name": "machineCards", "label": "Machine Cards", "type": "reference", "collections": ["machines"], "namespace": ["page", "viewMachines", "blocks", "machines", "machineCards"]}], "namespace": ["page", "viewMachines", "blocks", "machines"]}, {"label": "Quote Section", "name": "quoteSection", "fields": [{"type": "object", "label": "Request Quote Form", "name": "requestQuoteForm", "fields": [{"type": "string", "label": "Form Title", "name": "formTitle", "namespace": ["page", "viewMachines", "blocks", "quoteSection", "requestQuoteForm", "formTitle"]}, {"type": "string", "label": "Form Field 1 Placeholder", "name": "field1Placeholder", "namespace": ["page", "viewMachines", "blocks", "quoteSection", "requestQuoteForm", "field1Placeholder"]}, {"type": "string", "label": "Form Field 2 Placeholder", "name": "field2Placeholder", "namespace": ["page", "viewMachines", "blocks", "quoteSection", "requestQuoteForm", "field2Placeholder"]}, {"type": "string", "label": "Form Field 3 Placeholder", "name": "field3Placeholder", "namespace": ["page", "viewMachines", "blocks", "quoteSection", "requestQuoteForm", "field3Placeholder"]}, {"type": "string", "label": "Submit <PERSON>ton Text", "name": "submitButtonText", "namespace": ["page", "viewMachines", "blocks", "quoteSection", "requestQuoteForm", "submitButtonText"]}], "namespace": ["page", "viewMachines", "blocks", "quoteSection", "requestQuoteForm"]}], "namespace": ["page", "viewMachines", "blocks", "quoteSection"]}], "namespace": ["page", "viewMachines", "blocks"], "searchable": true, "uid": false}], "namespace": ["page", "viewMachines"]}, {"name": "careers", "label": "Careers", "fields": [{"name": "blocks", "label": "Blocks", "type": "object", "list": true, "templates": [{"label": "Hero Section", "name": "hero", "fields": [{"label": "Title", "name": "title", "type": "string", "namespace": ["page", "careers", "blocks", "hero", "title"]}, {"label": "Subtitle", "name": "subtitle", "type": "string", "namespace": ["page", "careers", "blocks", "hero", "subtitle"]}, {"label": "Hero Image", "name": "heroImage", "type": "image", "namespace": ["page", "careers", "blocks", "hero", "heroImage"]}], "namespace": ["page", "careers", "blocks", "hero"]}, {"label": "Why KM", "name": "whyKM", "fields": [{"label": "Title", "name": "title", "type": "string", "namespace": ["page", "careers", "blocks", "whyKM", "title"]}, {"label": "Description", "name": "description", "type": "string", "ui": {"component": "textarea"}, "namespace": ["page", "careers", "blocks", "whyKM", "description"]}], "namespace": ["page", "careers", "blocks", "whyKM"]}, {"label": "Openings Section", "name": "openings", "fields": [{"name": "noOpenings", "label": "No openings", "type": "object", "fields": [{"name": "heading", "label": "Heading", "type": "string", "namespace": ["page", "careers", "blocks", "openings", "noOpenings", "heading"]}, {"name": "description", "label": "Description", "type": "string", "ui": {"component": "textarea"}, "namespace": ["page", "careers", "blocks", "openings", "noOpenings", "description"]}, {"name": "buttonText", "label": "Button Text", "type": "string", "namespace": ["page", "careers", "blocks", "openings", "noOpenings", "buttonText"]}], "namespace": ["page", "careers", "blocks", "openings", "noOpenings"]}, {"name": "openingsList", "label": "Openings", "type": "object", "fields": [{"name": "heading", "label": "Section Heading", "type": "string", "namespace": ["page", "careers", "blocks", "openings", "openingsList", "heading"]}, {"name": "positions", "label": "Positions", "type": "object", "list": true, "ui": {}, "fields": [{"name": "title", "label": "Title", "type": "string", "namespace": ["page", "careers", "blocks", "openings", "openingsList", "positions", "title"]}, {"name": "subtitle", "label": "Subtitle", "type": "string", "ui": {"component": "textarea"}, "namespace": ["page", "careers", "blocks", "openings", "openingsList", "positions", "subtitle"]}, {"name": "jobDescriptionLink", "label": "Job Description Link", "type": "string", "namespace": ["page", "careers", "blocks", "openings", "openingsList", "positions", "jobDescriptionLink"]}], "namespace": ["page", "careers", "blocks", "openings", "openingsList", "positions"]}, {"name": "buttonText", "label": "<PERSON><PERSON> Text for every position", "type": "string", "namespace": ["page", "careers", "blocks", "openings", "openingsList", "buttonText"]}], "namespace": ["page", "careers", "blocks", "openings", "openingsList"]}], "namespace": ["page", "careers", "blocks", "openings"]}, {"label": "Benefits Section", "name": "benefits", "fields": [{"name": "heading", "label": "Heading", "type": "string", "namespace": ["page", "careers", "blocks", "benefits", "heading"]}, {"name": "benefits", "label": "Benefits", "type": "object", "list": true, "ui": {"max": 3}, "fields": [{"name": "benefitTitle", "type": "string", "label": "Benefit Title", "namespace": ["page", "careers", "blocks", "benefits", "benefits", "benefitTitle"]}, {"name": "icon", "type": "image", "label": "Icon", "namespace": ["page", "careers", "blocks", "benefits", "benefits", "icon"]}], "namespace": ["page", "careers", "blocks", "benefits", "benefits"]}], "namespace": ["page", "careers", "blocks", "benefits"]}], "namespace": ["page", "careers", "blocks"], "searchable": true, "uid": false}], "namespace": ["page", "careers"]}, {"name": "requestQuote", "label": "Request Quote", "fields": [{"name": "blocks", "label": "Blocks", "type": "object", "list": true, "templates": [{"label": "Quote Page Form", "name": "quotePageForm", "fields": [{"type": "string", "label": "Form Title", "name": "formTitle", "namespace": ["page", "requestQuote", "blocks", "quotePageForm", "formTitle"]}, {"type": "string", "label": "Form Field 1 Placeholder", "name": "field1Placeholder", "namespace": ["page", "requestQuote", "blocks", "quotePageForm", "field1Placeholder"]}, {"type": "string", "label": "Form Field 2 Placeholder", "name": "field2Placeholder", "namespace": ["page", "requestQuote", "blocks", "quotePageForm", "field2Placeholder"]}, {"type": "string", "label": "Form Field 3 Placeholder", "name": "field3Placeholder", "namespace": ["page", "requestQuote", "blocks", "quotePageForm", "field3Placeholder"]}, {"type": "string", "label": "Submit <PERSON>ton Text", "name": "submitButtonText", "namespace": ["page", "requestQuote", "blocks", "quotePageForm", "submitButtonText"]}], "namespace": ["page", "requestQuote", "blocks", "quotePageForm"]}, {"label": "Quote Page Message", "name": "quotePageMessage", "fields": [{"type": "string", "label": "Message Title", "name": "messageTitle", "namespace": ["page", "requestQuote", "blocks", "quotePageMessage", "messageTitle"]}, {"type": "string", "label": "Message Body", "name": "messageBody", "namespace": ["page", "requestQuote", "blocks", "quotePageMessage", "messageBody"]}], "namespace": ["page", "requestQuote", "blocks", "quotePageMessage"]}], "namespace": ["page", "requestQuote", "blocks"], "searchable": true, "uid": false}], "namespace": ["page", "requestQuote"]}], "namespace": ["page"]}, {"label": "Machines", "name": "machines", "path": "content/machines", "ui": {"allowedActions": {"create": false, "delete": false}}, "fields": [{"type": "object", "label": "Machine Cards", "name": "machineCard", "list": true, "ui": {}, "fields": [{"type": "string", "label": "Machine Name", "name": "name", "required": true, "namespace": ["machines", "machineCard", "name"], "searchable": true, "uid": false}, {"type": "image", "label": "Machine Image", "name": "image", "required": true, "namespace": ["machines", "machineCard", "image"], "searchable": false, "uid": false}, {"type": "rich-text", "label": "Machine Description", "name": "description", "required": true, "description": "Please select Bullet List in the input field to add Machine's description in the correct format", "namespace": ["machines", "machineCard", "description"], "searchable": true, "parser": {"type": "markdown"}, "uid": false}, {"type": "string", "label": "Machine Type", "name": "type", "required": true, "description": "Existing types include Mill, Lathe, Inspection, Engraver, and Bandsaw", "namespace": ["machines", "machineCard", "type"], "searchable": true, "uid": false}], "namespace": ["machines", "machineCard"], "searchable": true, "uid": false}], "namespace": ["machines"]}, {"label": "Footer", "name": "footer", "path": "content/footer", "ui": {"allowedActions": {"create": false, "delete": false}, "global": true}, "fields": [{"type": "image", "label": "Logo", "name": "logo", "namespace": ["footer", "logo"], "searchable": false, "uid": false}, {"type": "object", "label": "Contact info", "name": "contactInfo", "list": true, "ui": {}, "fields": [{"type": "string", "label": "Category heading", "name": "heading", "namespace": ["footer", "contactInfo", "heading"], "searchable": true, "uid": false}, {"type": "rich-text", "label": "Category content", "name": "content", "namespace": ["footer", "contactInfo", "content"], "searchable": true, "parser": {"type": "markdown"}, "uid": false}], "namespace": ["footer", "contactInfo"], "searchable": true, "uid": false}, {"type": "object", "label": "Navigation links", "name": "navLinks", "list": true, "ui": {}, "fields": [{"type": "string", "label": "Link label", "name": "label", "namespace": ["footer", "navLinks", "label"], "searchable": true, "uid": false}, {"type": "string", "label": "Page link", "name": "href", "namespace": ["footer", "navLinks", "href"], "searchable": true, "uid": false}], "namespace": ["footer", "navLinks"], "searchable": true, "uid": false}], "namespace": ["footer"]}], "config": {"media": {"tina": {"publicFolder": "public", "mediaRoot": ""}}}}, "lookup": {"DocumentConnection": {"type": "DocumentConnection", "resolveType": "multiCollectionDocumentList", "collections": ["page", "machines", "footer"]}, "Node": {"type": "Node", "resolveType": "nodeDocument"}, "DocumentNode": {"type": "DocumentNode", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "PageHomeBlocksMachineCarouselMachineCards": {"type": "PageHomeBlocksMachineCarouselMachineCards", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "PageHomeBlocks": {"type": "PageHomeBlocks", "resolveType": "unionData", "typeMap": {"hero": "PageHomeBlocksHero", "values": "PageHomeBlocksValues", "location": "PageHomeBlocksLocation", "machineCarousel": "PageHomeBlocksMachineCarousel", "quoteSection": "PageHomeBlocksQuoteSection", "careerSection": "PageHomeBlocksCareerSection"}}, "PageViewMachinesBlocksMachinesMachineCards": {"type": "PageViewMachinesBlocksMachinesMachineCards", "resolveType": "multiCollectionDocument", "createDocument": "create", "updateDocument": "update"}, "PageViewMachinesBlocks": {"type": "PageViewMachinesBlocks", "resolveType": "unionData", "typeMap": {"machines": "PageViewMachinesBlocksMachines", "quoteSection": "PageViewMachinesBlocksQuoteSection"}}, "PageCareersBlocks": {"type": "PageCareersBlocks", "resolveType": "unionData", "typeMap": {"hero": "PageCareersBlocksHero", "whyKM": "PageCareersBlocksWhyKM", "openings": "PageCareersBlocksOpenings", "benefits": "PageCareersBlocksBenefits"}}, "PageRequestQuoteBlocks": {"type": "PageRequestQuoteBlocks", "resolveType": "unionData", "typeMap": {"quotePageForm": "PageRequestQuoteBlocksQuotePageForm", "quotePageMessage": "PageRequestQuoteBlocksQuotePageMessage"}}, "Page": {"type": "Page", "resolveType": "unionData", "collection": "page", "typeMap": {"home": "PageHome", "viewMachines": "PageViewMachines", "careers": "PageCareers", "requestQuote": "PageRequestQuote"}}, "PageConnection": {"type": "PageConnection", "resolveType": "collectionDocumentList", "collection": "page"}, "Machines": {"type": "Machines", "resolveType": "collectionDocument", "collection": "machines", "createMachines": "create", "updateMachines": "update"}, "MachinesConnection": {"type": "MachinesConnection", "resolveType": "collectionDocumentList", "collection": "machines"}, "Footer": {"type": "Footer", "resolveType": "collectionDocument", "collection": "footer", "createFooter": "create", "updateFooter": "update"}, "FooterConnection": {"type": "FooterConnection", "resolveType": "collectionDocumentList", "collection": "footer"}}, "graphql": {"kind": "Document", "definitions": [{"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "Reference"}, "description": {"kind": "StringValue", "value": "References another document, used as a foreign key"}, "directives": []}, {"kind": "ScalarTypeDefinition", "name": {"kind": "Name", "value": "JSON"}, "description": {"kind": "StringValue", "value": ""}, "directives": []}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "SystemInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "filename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "basename"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasReferences"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "breadcrumbs"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "excludeExtension"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "relativePath"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "extension"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "template"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Folder"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasPreviousPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "hasNextPage"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "startCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "endCursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Node"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": ""}, "name": {"kind": "Name", "value": "Document"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InterfaceTypeDefinition", "description": {"kind": "StringValue", "value": "A relay-compliant pagination connection"}, "name": {"kind": "Name", "value": "Connection"}, "interfaces": [], "directives": [], "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Query"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "getOptimizedQuery"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "queryString"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "collections"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "ListType", "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Collection"}}}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "id"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "document"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "page"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Page"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON>er"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "machines"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "machinesConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesFilter"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesConnection"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "footer"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "footerConnection"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer<PERSON><PERSON>er"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterConnection"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "page"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON>er"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer<PERSON><PERSON>er"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "DocumentConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "DocumentConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Collection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "slug"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "path"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "format"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "matches"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "templates"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "fields"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "documents"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "before"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "after"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "first"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "last"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sort"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "filter"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "folder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentConnection"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "DocumentNode"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "PageHome"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachines"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareers"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuote"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Folder"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksHero"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "image"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality1"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality2"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality3"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksValues"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "backgroundImage"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "quality1"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality1"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "quality2"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality2"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "quality3"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality3"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksLocation"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "location"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselMachineCards"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarousel"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "sectionTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "buttonLabel"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "machineCards"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselMachineCards"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionRequestQuoteForm"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "formTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "requestQuoteForm"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionRequestQuoteForm"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "requestCopyTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "fillFormCopy"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "emailCopy"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "responseCopy"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard1"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "card1Description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard2"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "card2Description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard3"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "card3Description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageHomeBlocksCareerSection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "careerDescription"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "buttonLabel"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "card1"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard1"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "card2"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard2"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "card3"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard3"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocks"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksHero"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValues"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksLocation"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarousel"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSection"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSection"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "PageHome"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "blocks"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesMachineCards"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachines"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "machineCards"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesMachineCards"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionRequestQuoteForm"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "formTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "requestQuoteForm"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionRequestQuoteForm"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocks"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachines"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSection"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "PageViewMachines"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "blocks"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksHero"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "subtitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heroImage"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksWhyKM"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsNoOpenings"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "buttonText"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListPositions"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "title"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "subtitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "jobDescriptionLink"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsList"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "positions"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListPositions"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "buttonText"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksOpenings"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "noOpenings"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsNoOpenings"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "openingsList"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsList"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsBenefits"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "benefitTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "icon"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageCareersBlocksBenefits"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "benefits"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsBenefits"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocks"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksHero"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksWhyKM"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpenings"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksBenefits"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "PageCareers"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "blocks"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageForm"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "formTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageMessage"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "messageTitle"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "messageBody"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocks"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageForm"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageMessage"}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "PageRequestQuote"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "blocks"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "UnionTypeDefinition", "name": {"kind": "Name", "value": "Page"}, "directives": [], "types": [{"kind": "NamedType", "name": {"kind": "Name", "value": "PageHome"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachines"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareers"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuote"}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "ImageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "StringFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "in"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksHeroFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "RichTextFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "startsWith"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "eq"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "exists"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Boolean"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality1Filter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality2Filter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality3Filter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "backgroundImage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quality1"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality1Filter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quality2"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality2Filter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quality3"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality3Filter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksLocationFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "location"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselMachineCardsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sectionTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonLabel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCards"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselMachineCardsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionRequestQuoteFormFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "formTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestQuoteForm"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionRequestQuoteFormFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestCopyTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "fillFormCopy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "emailCopy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "responseCopy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard1Filter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card1Description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard2Filter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card2Description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard3Filter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card3Description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careerDescription"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonLabel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card1"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard1Filter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card2"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard2Filter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card3"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard3Filter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksHeroFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "values"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "location"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksLocationFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCarousel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quoteSection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careerSection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesMachineCardsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCards"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesMachineCardsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionRequestQuoteFormFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "formTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestQuoteForm"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionRequestQuoteFormFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quoteSection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachines<PERSON><PERSON>er"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksHeroFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heroImage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksWhyKMFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsNoOpeningsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListPositionsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "jobDescriptionLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "positions"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListPositionsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "noOpenings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsNoOpeningsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "openingsList"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsBenefitsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "benefitTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "icon"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "benefits"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsBenefitsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksHeroFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "whyKM"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksWhyKMFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "openings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "benefits"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageFormFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "formTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageMessageFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "messageTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "messageBody"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quotePageForm"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageFormFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quotePageMessage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageMessageFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON>er"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "home"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "viewMachines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachines<PERSON><PERSON>er"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careers"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestQuote"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "PageConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Page"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "PageConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "MachinesMachineCard"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "name"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "image"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "description"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "type"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Machines"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "machineCard"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMachineCard"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MachinesMachineCardFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "name"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MachinesFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCard"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMachineCardFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "MachinesConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "MachinesConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "FooterContactInfo"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "heading"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "content"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "FooterNavLinks"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "label"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "href"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Node"}}, {"kind": "NamedType", "name": {"kind": "Name", "value": "Document"}}], "directives": [], "name": {"kind": "Name", "value": "Footer"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "logo"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "contactInfo"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterContactInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "navLinks"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterNavLinks"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "id"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ID"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_sys"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "SystemInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "_values"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "FooterContactInfoFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "content"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "RichTextFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "FooterNavLinksFilter"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "StringFilter"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "Footer<PERSON><PERSON>er"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "logo"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "ImageFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "contactInfo"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterContactInfoFilter"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "navLinks"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterNavLinksFilter"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "FooterConnectionEdges"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "cursor"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "node"}, "arguments": [], "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer"}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [{"kind": "NamedType", "name": {"kind": "Name", "value": "Connection"}}], "directives": [], "name": {"kind": "Name", "value": "FooterConnection"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "pageInfo"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageInfo"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "totalCount"}, "arguments": [], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Float"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "edges"}, "arguments": [], "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterConnectionEdges"}}}}]}, {"kind": "ObjectTypeDefinition", "interfaces": [], "directives": [], "name": {"kind": "Name", "value": "Mutation"}, "fields": [{"kind": "FieldDefinition", "name": {"kind": "Name", "value": "addPendingDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "template"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "deleteDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createDocument"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createFolder"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "collection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "DocumentNode"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updatePage"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Page"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createPage"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Page"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateMachines"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createMachines"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Machines"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "updateFooter"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer"}}}}, {"kind": "FieldDefinition", "name": {"kind": "Name", "value": "createFooter"}, "arguments": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "params"}, "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterMutation"}}}}], "type": {"kind": "NonNullType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "Footer"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentUpdateMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "page"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "relativePath"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "DocumentMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "page"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "footer"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksHeroMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality1Mutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality2Mutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality3Mutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksValuesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "backgroundImage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quality1"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality1Mutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quality2"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality2Mutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quality3"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesQuality3Mutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksLocationMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "location"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "sectionTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonLabel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCards"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionRequestQuoteFormMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "formTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestQuoteForm"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionRequestQuoteFormMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestCopyTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "fillFormCopy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "emailCopy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "responseCopy"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard1Mutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card1Description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard2Mutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card2Description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard3Mutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card3Description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careerDescription"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonLabel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card1"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard1Mutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card2"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard2Mutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "card3"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionCard3Mutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeBlocksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksHeroMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "values"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksValuesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "location"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksLocationMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCarousel"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksMachineCarouselMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quoteSection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksQuoteSectionMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careerSection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksCareerSectionMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageHomeMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeBlocksMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCards"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionRequestQuoteFormMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "formTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestQuoteForm"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionRequestQuoteFormMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMachinesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quoteSection"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksQuoteSectionMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageViewMachinesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesBlocksMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksHeroMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heroImage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksWhyKMMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsNoOpeningsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListPositionsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "title"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "subtitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "jobDescriptionLink"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "positions"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListPositionsMutation"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "buttonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "noOpenings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsNoOpeningsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "openingsList"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsOpeningsListMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsBenefitsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "benefitTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "icon"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "benefits"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsBenefitsMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersBlocksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "hero"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksHeroMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "whyKM"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksWhyKMMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "openings"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksOpeningsMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "benefits"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksBenefitsMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageCareersMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersBlocksMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageFormMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "formTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field1Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field2Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "field3Placeholder"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "submitButtonText"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageMessageMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "messageTitle"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "messageBody"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quotePageForm"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageFormMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "quotePageMessage"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksQuotePageMessageMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageRequestQuoteMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "blocks"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteBlocksMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "PageMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "home"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageHomeMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "viewMachines"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageViewMachinesMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "careers"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageCareersMutation"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "requestQuote"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "PageRequestQuoteMutation"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MachinesMachineCardMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "name"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "image"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "description"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "type"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "MachinesMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "machineCard"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "MachinesMachineCardMutation"}}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "FooterContactInfoMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "heading"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "content"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "JSON"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "FooterNavLinksMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "label"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "href"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}]}, {"kind": "InputObjectTypeDefinition", "name": {"kind": "Name", "value": "FooterMutation"}, "fields": [{"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "logo"}, "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "String"}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "contactInfo"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterContactInfoMutation"}}}}, {"kind": "InputValueDefinition", "name": {"kind": "Name", "value": "navLinks"}, "type": {"kind": "ListType", "type": {"kind": "NamedType", "name": {"kind": "Name", "value": "FooterNavLinksMutation"}}}}]}]}}