{"name": "kenworthy-machine", "version": "0.1.0", "private": true, "scripts": {"dev": "TINA_PUBLIC_IS_LOCAL=true tinacms dev -c \"next dev\"", "build": "tinacms build && next build", "start": "tinacms build && next start", "lint": "next lint", "format:check": "prettier . --check", "format": "prettier . --write", "prepare": "husky install"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "framer-motion": "^10.18.0", "next": "^14.2.23", "nodemailer": "^6.10.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "swiper": "^11.2.1", "tinacms": "^2.1.1"}, "devDependencies": {"@tinacms/cli": "^1.5.53", "@types/node": "^20.17.16", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.0.4", "eslint-config-prettier": "^9.1.0", "husky": "^8.0.3", "lint-staged": "^15.4.2", "prettier": "3.4.2", "typescript": "^5"}}