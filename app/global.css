/* CSS variables based on design system requirements */

:root {
  --border-radius: 20px;
  --box-shadow: 0px 6px 4px #00000040;
  --background-color-dark: #0b113e;
  --background-color-med: #232952;
  --background-color-light: #b9c1c6;
  --text-color: #0b113e;
  --text-color-light: #586874;
  --primary-red: #db0a40;
}

.swiper-button-prev {
  color: var(--background-color-med);
  border-radius: 50%;
  min-width: 75px;
  min-height: 75px;
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--box-shadow);
}

.swiper-button-next {
  color: var(--background-color-med);
  border-radius: 50%;
  min-width: 75px;
  min-height: 75px;
  background-color: rgba(255, 255, 255, 0.3);
  box-shadow: var(--box-shadow);
}

.swiper-slide,
.swiper-slide-active {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
