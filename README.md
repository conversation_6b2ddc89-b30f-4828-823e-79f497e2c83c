# Kenworthy Machine Website

## Overview

This repository contains the source code for the Kenworthy Machine website, a small business located near Seattle, Washington specializing in precision machine manufacturing. Wavecrest Web Collective's overhaul of the company's site, using web design by <PERSON><PERSON>, serves to better showcase the company's services, values, and product offerings to potential clients and partners.

## Live Site

The website is live and can be visited at the following URL: [Kenworthy Machine Live Site](https://kenworthymachine.com/)

## Team

- **<PERSON>** - Developer ([Alex's GitHub](https://github.com/alibacova))
- **<PERSON><PERSON>** - Developer ([Archaa's GitHub](https://github.com/avinashi10))
- **<PERSON>** - Developer ([<PERSON>'s GitHub](https://github.com/BrettEastman))
- **<PERSON><PERSON>** - Designer ([<PERSON><PERSON>'s Portfolio](https://kaylashovlowsky.wixsite.com/kaylashovlowskyuxdes))


## Tools and Tech Stack

### Primary
<div>
  <img src="https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB" />
  <img src='https://img.shields.io/badge/javascript-%23323330.svg?style=for-the-badge&logo=javascript&logoColor=%23F7DF1E' />
  <img src='https://img.shields.io/badge/Next.js-000?logo=nextdotjs&logoColor=fff&style=for-the-badge' />
  <img src='https://img.shields.io/badge/html5-%23E34F26.svg?style=for-the-badge&logo=html5&logoColor=white' />
  <img src='https://img.shields.io/badge/css3-%231572B6.svg?style=for-the-badge&logo=css3&logoColor=white' />
  <img src='https://img.shields.io/badge/Chakra%20UI-319795?logo=chakraui&logoColor=fff&style=for-the-badge' />
</div>

### Tools
<div>
  <img src="https://img.shields.io/badge/Tina-EC4815?logo=tina&logoColor=fff&style=for-the-badge" />
</div>

### Miscellaneous
<div>
  <img src="https://img.shields.io/badge/eslint-3A33D1?style=for-the-badge&logo=eslint&logoColor=white" />
  <img src="https://img.shields.io/badge/Prettier-F7B93E?logo=prettier&logoColor=fff&style=for-the-badge" />
  <img src="https://img.shields.io/badge/Jira-0052CC?style=for-the-badge&logo=Jira&logoColor=white" />
  <img src="https://img.shields.io/badge/Figma-F24E1E?style=for-the-badge&logo=figma&logoColor=white" />
</div>

## Notable Features

- **TinaCMS Integration**: Provides a powerful editing interface for site administrators to update content without needing to touch the codebase.
- **Dynamic Form Handling**: Uses Nodemailer to manage form submissions, enabling the company to receive quote requests directly from the website.
- **Responsive Design**: The site is fully responsive, ensuring a seamless experience across all devices and screen sizes.

## Contributions

Contributions are welcome! If you have suggestions or want to contribute to the project, please create a pull request or open an issue.

To get a local copy up and running, follow these simple steps.

### Prerequisites

Before you begin, ensure you have the following installed:
- Node.js
- npm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/wavecrestweb/kenworthy-machine.git
```
2. Navigate to the project directory:
```bash
cd kenworthy-machine-website
```
3. Install NPM packages:
```bash
npm install
```
4. Start the development server:
```bash
npm run dev
```
5. Open your browser and navigate to http://localhost:3000/admin

## License

© 2024, Wavecrest Web Collective & Kayla Shovlowsky. All Rights Reserved.

Unauthorized copying, modification, distribution, or use of this software is strictly prohibited. While contributions to the project are welcome, any work containing or derived from the contents of this repository remains the exclusive property of the original authors.

